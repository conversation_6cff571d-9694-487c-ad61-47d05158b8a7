<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>域名访问测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
        #log { background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6; border-radius: 5px; max-height: 400px; overflow-y: auto; }
    </style>
</head>
<body>
    <h1>域名访问测试页面</h1>
    
    <div class="test-result info">
        <h3>当前访问信息</h3>
        <p><strong>当前域名:</strong> <span id="currentDomain"></span></p>
        <p><strong>当前协议:</strong> <span id="currentProtocol"></span></p>
        <p><strong>完整URL:</strong> <span id="currentUrl"></span></p>
        <p><strong>User Agent:</strong> <span id="userAgent"></span></p>
    </div>

    <div>
        <h3>测试操作</h3>
        <button onclick="testCreateOrder()">测试创建订单</button>
        <button onclick="testSecurityCheck()">测试安全检查</button>
        <button onclick="clearLog()">清空日志</button>
    </div>

    <div>
        <h3>测试日志</h3>
        <div id="log"></div>
    </div>

    <script>
        // 显示当前访问信息
        document.getElementById('currentDomain').textContent = window.location.hostname;
        document.getElementById('currentProtocol').textContent = window.location.protocol;
        document.getElementById('currentUrl').textContent = window.location.href;
        document.getElementById('userAgent').textContent = navigator.userAgent;

        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleString();
            const logEntry = document.createElement('div');
            logEntry.className = `test-result ${type}`;
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        async function testCreateOrder() {
            log('开始测试创建订单...', 'info');
            
            try {
                const response = await fetch('/Index/Utils/createOrder', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        customer_contact: '<EMAIL>',
                        product_id: '85',
                        pay_type: 'wxpay'
                    })
                });

                const data = await response.json();
                
                if (data.status === 'success') {
                    log(`✅ 订单创建成功！订单号: ${data.data.order_info.order_id}`, 'success');
                    log(`💰 商品: ${data.data.order_info.product_name}, 价格: ${data.data.order_info.product_price}`, 'info');
                } else {
                    log(`❌ 订单创建失败: ${data.message}`, 'error');
                }
            } catch (error) {
                log(`🚫 网络错误: ${error.message}`, 'error');
            }
        }

        async function testSecurityCheck() {
            log('开始测试安全检查...', 'info');
            log(`🔍 当前Referer: ${document.referrer || '无'}`, 'info');
            log(`🌐 当前Host: ${window.location.host}`, 'info');
            
            // 模拟一个简单的请求来触发安全检查
            try {
                const response = await fetch('/Index/Utils/createOrder', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        customer_contact: '<EMAIL>',
                        product_id: '85',
                        pay_type: 'wxpay'
                    })
                });

                if (response.ok) {
                    log('✅ 安全检查通过，请求被接受', 'success');
                } else {
                    log(`❌ 安全检查失败，HTTP状态: ${response.status}`, 'error');
                }
            } catch (error) {
                log(`🚫 安全检查测试失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时自动记录访问信息
        window.onload = function() {
            log('页面加载完成', 'success');
            log(`访问方式: ${window.location.hostname === 'lhzs.lol' ? '域名访问' : 'IP访问'}`, 'info');
        };
    </script>
</body>
</html>
