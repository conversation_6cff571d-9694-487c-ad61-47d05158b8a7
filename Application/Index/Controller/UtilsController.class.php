<?php
namespace Index\Controller;
use Think\Controller;
class UtilsController extends Controller {
    public function index(){

    	// 获取所有工具数据用于工具库页面展示
    	$all_tools = M("app")->order("addtime desc")->select();
		$this->assign("all_tools",$all_tools);

    	// 获取配置信息
		$con =M("config")->where("id=1")->find();
		$this->assign("con",$con);
		$this->display("index");
    }
    
	    public function type(){
	    
		    $id = $_GET['id'];
		    $mod = M("app")->where("type={$id}")->order("addtime desc")->select();
		    $this->assign("mod",$mod);
		    $type = M("type")->select();
	    	$this->assign("type",$type);
	    	$con =M("config")->where("id=1")->find()['webname'];
			 $this->assign("con",$con);
		    $this->display("type");
	    
	    }
	    
	     public function h5(){
        	$type = M("type")->select();
    	  $this->assign("type",$type);
        	$m=M('type');  
			$m1=M('app');  
			$parent=$m->order('sort DESC')->select();  
			 foreach($parent as $n=> $val){  
				 $id = $val['id'];
				 $parent[$n]['list']=$m1->where("type='{$id}'")->order("addtime DESC")->limit(10)->select();  
			 } 
			 $this->assign("app",$parent);
			 $con =M("config")->where("id=1")->find()['webname'];
			 $this->assign("con",$con);
         $this->display("h5");
    	}
    	
    	public function h5t(){
        	 $id = $_GET['id'];
		    $mod = M("app")->where("type={$id}")->order("addtime desc")->select();
		    $this->assign("mod",$mod);
		    $type = M("type")->select();
	    	$this->assign("type",$type);
	    	$con =M("config")->where("id=1")->find()['webname'];
			 $this->assign("con",$con);
		    $this->display("h5t");
    	}

    	public function vip(){
    		// 获取配置信息
			$con =M("config")->where("id=1")->find();
			$this->assign("con",$con);
			$this->display("vip");
    	}

    	/**
    	 * 初始化数据库表
    	 */
    	public function initDatabase(){
    		$db = M();

    		// 创建订单状态表
    		$sql = "CREATE TABLE IF NOT EXISTS `nav_order_status` (
    			`id` int(11) NOT NULL AUTO_INCREMENT,
    			`order_id` varchar(100) NOT NULL COMMENT '订单ID',
    			`product_type` varchar(10) NOT NULL COMMENT '产品类型：1=38天会员，2=永久会员',
    			`status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '状态：pending=待处理，processing=处理中，completed=已完成，failed=失败',
    			`token` varchar(50) DEFAULT NULL COMMENT '生成的会员Token',
    			`reg_time` datetime DEFAULT NULL COMMENT '注册时间',
    			`expire_time` datetime DEFAULT NULL COMMENT '到期时间',
    			`result_data` text COMMENT '完整的API返回数据',
    			`error_message` varchar(500) DEFAULT NULL COMMENT '错误信息',
    			`client_ip` varchar(45) DEFAULT NULL COMMENT '客户端IP',
    			`created_time` datetime NOT NULL COMMENT '创建时间',
    			`updated_time` datetime NOT NULL COMMENT '更新时间',
    			PRIMARY KEY (`id`),
    			UNIQUE KEY `order_id` (`order_id`),
    			KEY `status` (`status`),
    			KEY `created_time` (`created_time`)
    		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单状态跟踪表';";

    		try {
    			$db->execute($sql);
    			echo "数据库表创建成功！";
    		} catch (Exception $e) {
    			echo "数据库表创建失败：" . $e->getMessage();
    		}
    	}

    	/**
    	 * 创建订单代理接口
    	 */
    	public function createOrder(){
    		// 只允许POST请求
    		if(!IS_POST){
    			$this->ajaxReturn(array('status' => 'error', 'message' => '请求方式错误'));
    			return;
    		}

    		// 基础安全检查
    		if(!$this->securityCheck()){
    			$this->ajaxReturn(array('status' => 'error', 'message' => '请求被拒绝'));
    			return;
    		}

    		// 获取参数
    		$customerContact = I('post.customer_contact', '<EMAIL>', 'trim');
    		$productId = I('post.product_id', '', 'trim');
    		$payType = I('post.pay_type', '', 'trim');

    		// 参数验证
    		if(empty($productId) || empty($payType)){
    			$this->ajaxReturn(array('status' => 'error', 'message' => '参数不完整'));
    			return;
    		}

    		// 验证产品ID
    		if(!in_array($productId, ['85', '86'])){
    			$this->ajaxReturn(array('status' => 'error', 'message' => '产品ID无效'));
    			return;
    		}

    		// 验证支付类型
    		if(!in_array($payType, ['wxpay', 'alipay'])){
    			$this->ajaxReturn(array('status' => 'error', 'message' => '支付类型无效'));
    			return;
    		}

    		// 调用创建订单API
    		try {
    			$params = array(
    				'customer_contact' => $customerContact,
    				'product_id' => $productId,
    				'pay_type' => $payType
    			);

    			$url = "https://cloudshop.qnm6.top/create_order.php?" . http_build_query($params);
    			$response = $this->httpGet($url);
    			$data = json_decode($response, true);

    			// 记录日志
    			$this->logApiCall('createOrder', $params, $data);

    			if($data && isset($data['status'])){
    				$this->ajaxReturn($data);
    			} else {
    				$result = array('status' => 'error', 'message' => '订单创建失败');
    				$this->logApiCall('createOrder_error', $params, $result);
    				$this->ajaxReturn($result);
    			}
    		} catch (Exception $e) {
    			$result = array('status' => 'error', 'message' => '订单服务异常');
    			$this->logApiCall('createOrder_exception', array('product_id' => $productId), $result);
    			$this->ajaxReturn($result);
    		}
    	}

    	/**
    	 * 支付状态检查代理接口
    	 */
    	public function checkPayment(){
    		// 只允许POST请求
    		if(!IS_POST){
    			$this->ajaxReturn(array('status' => 'error', 'message' => '请求方式错误'));
    			return;
    		}

    		// 基础安全检查
    		if(!$this->securityCheck()){
    			$this->ajaxReturn(array('status' => 'error', 'message' => '请求被拒绝'));
    			return;
    		}

    		// 获取订单号
    		$orderId = I('post.order_id', '', 'trim');
    		if(empty($orderId)){
    			$this->ajaxReturn(array('status' => 'error', 'message' => '订单号不能为空'));
    			return;
    		}

    		// 调用支付状态检查API
    		try {
    			$url = "https://cloudshop.qnm6.top/check_payment_status.php?order_id=" . urlencode($orderId);
    			$response = $this->httpGet($url);
    			$data = json_decode($response, true);

    			if($data && isset($data['status'])){
    				$this->ajaxReturn($data);
    			} else {
    				$this->ajaxReturn(array('status' => 'error', 'message' => '支付状态查询失败'));
    			}
    		} catch (Exception $e) {
    			$this->ajaxReturn(array('status' => 'error', 'message' => '查询服务异常'));
    		}
    	}

    	/**
    	 * 会员注册代理接口
    	 * 作为前端和真实API之间的安全代理
    	 */
    	public function registerVip(){
    		// 只允许POST请求
    		if(!IS_POST){
    			$this->ajaxReturn(array('code' => 400, 'message' => '请求方式错误'));
    			return;
    		}

    		// 基础安全检查
    		if(!$this->securityCheck()){
    			$this->ajaxReturn(array('code' => 400, 'message' => '请求被拒绝'));
    			return;
    		}

    		// 获取参数
    		$orderId = I('post.order_id', '', 'trim');
    		$productType = I('post.product_type', '', 'trim');

    		// 参数验证
    		if(empty($orderId) || empty($productType)){
    			$this->ajaxReturn(array('code' => 400, 'message' => '参数不完整'));
    			return;
    		}

    		// 验证产品类型
    		if(!in_array($productType, ['1', '2'])){
    			$this->ajaxReturn(array('code' => 400, 'message' => '产品类型错误'));
    			return;
    		}

    		// 检查订单是否已经处理过
    		$orderModel = M('order_status');
    		$existingOrder = $orderModel->where(array('order_id' => $orderId))->find();

    		if($existingOrder){
    			if($existingOrder['register_status'] == 'success'){
    				$this->ajaxReturn(array('code' => 400, 'message' => '该订单已经处理过，不能重复注册'));
    				return;
    			} else if($existingOrder['register_status'] == 'pending' &&
    					  $existingOrder['processed_count'] > 0 &&
    					  (time() - strtotime($existingOrder['last_check_time'])) < 60){
    				$this->ajaxReturn(array('code' => 400, 'message' => '该订单正在处理中，请稍后再试'));
    				return;
    			}
    		}

    		// 验证订单状态（先验证订单是否已支付）
    		$paymentStatus = $this->checkOrderPaymentStatus($orderId);
    		if(!$paymentStatus){
    			$this->ajaxReturn(array('code' => 400, 'message' => '订单未支付或不存在'));
    			return;
    		}

    		// 标记订单为处理中状态
    		$this->markOrderAsProcessing($orderId, $productType);

    		// 调用真实的注册API
    		$result = $this->callVipRegisterAPI($productType);

    		// 根据结果更新订单状态
    		if($result['code'] == 200){
    			$this->markOrderAsCompleted($orderId, $result);
    		} else {
    			$this->markOrderAsFailed($orderId, $result['message']);
    		}

    		// 返回结果
    		$this->ajaxReturn($result);
    	}

    	/**
    	 * 检查订单支付状态
    	 */
    	private function checkOrderPaymentStatus($orderId){
    		try {
    			$url = "https://cloudshop.qnm6.top/check_payment_status.php?order_id=" . urlencode($orderId);
    			$response = $this->httpGet($url);
    			$data = json_decode($response, true);

    			return isset($data['status']) && $data['status'] === 'success' &&
    				   isset($data['data']['order_status']) && $data['data']['order_status'] === 'paid';
    		} catch (Exception $e) {
    			return false;
    		}
    	}

    	/**
    	 * 调用真实的VIP注册API
    	 */
    	private function callVipRegisterAPI($productType){
    		try {
    			$url = "https://api.qnm6.top/admapi/vipregister.php?lx=" . urlencode($productType);
    			$response = $this->httpGet($url);
    			$data = json_decode($response, true);

    			if($data && isset($data['code'])){
    				return $data;
    			} else {
    				return array('code' => 500, 'message' => '注册服务异常');
    			}
    		} catch (Exception $e) {
    			return array('code' => 500, 'message' => '注册失败：' . $e->getMessage());
    		}
    	}

    	/**
    	 * 标记订单为处理中状态
    	 */
    	private function markOrderAsProcessing($orderId, $productType){
    		$orderModel = M('order_status');
    		$data = array(
    			'order_id' => $orderId,
    			'product_type' => $productType,
    			'register_status' => 'pending',
    			'client_ip' => $this->getClientIP(),
    			'user_agent' => isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '',
    			'processed_count' => 1,
    			'last_check_time' => date('Y-m-d H:i:s')
    		);

    		$existingOrder = $orderModel->where(array('order_id' => $orderId))->find();
    		if($existingOrder){
    			$data['processed_count'] = intval($existingOrder['processed_count']) + 1;
    			$orderModel->where(array('order_id' => $orderId))->save($data);
    		} else {
    			$orderModel->add($data);
    		}
    	}

    	/**
    	 * 标记订单为完成状态
    	 */
    	private function markOrderAsCompleted($orderId, $result){
    		$orderModel = M('order_status');
    		$data = array(
    			'register_status' => 'success',
    			'vip_token' => isset($result['token']) ? $result['token'] : '',
    			'reg_time' => isset($result['reg_time']) ? $result['reg_time'] : '',
    			'expire_time' => isset($result['expire_time']) ? $result['expire_time'] : '',
    			'last_check_time' => date('Y-m-d H:i:s')
    		);
    		$orderModel->where(array('order_id' => $orderId))->save($data);
    	}

    	/**
    	 * 标记订单为失败状态
    	 */
    	private function markOrderAsFailed($orderId, $errorMessage){
    		$orderModel = M('order_status');
    		$data = array(
    			'register_status' => 'failed',
    			'last_check_time' => date('Y-m-d H:i:s')
    		);
    		$orderModel->where(array('order_id' => $orderId))->save($data);
    	}

    	/**
    	 * 基础安全检查
    	 */
    	private function securityCheck(){
    		// 检查请求来源
    		$referer = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : '';
    		$host = isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : '';

    		// 记录调试信息
    		$debugInfo = array(
    			'referer' => $referer,
    			'host' => $host,
    			'user_agent' => isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '',
    			'client_ip' => $this->getClientIP()
    		);
    		$this->logApiCall('security_check_debug', $debugInfo, 'checking');

    		// 确保请求来自本站 - 修复strpos返回0的问题
    		if(!empty($referer) && strpos($referer, $host) === false){
    			$this->logApiCall('security_check_failed', $debugInfo, 'referer_check_failed');
    			return false;
    		}

    		// 检查User-Agent，防止简单的机器人请求
    		$userAgent = isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '';
    		if(empty($userAgent) || strlen($userAgent) < 10){
    			$this->logApiCall('security_check_failed', $debugInfo, 'user_agent_check_failed');
    			return false;
    		}

    		// 简单的频率限制（基于IP）
    		$clientIP = $this->getClientIP();
    		$cacheKey = 'api_limit_' . md5($clientIP);
    		$requestCount = S($cacheKey);

    		if($requestCount === false){
    			// 第一次请求，设置计数器，5分钟内最多20次请求
    			S($cacheKey, 1, 300);
    		} else if($requestCount >= 20){
    			// 超过限制
    			$this->logApiCall('security_check_failed', $debugInfo, 'rate_limit_exceeded: ' . $requestCount);
    			return false;
    		} else {
    			// 增加计数
    			S($cacheKey, $requestCount + 1, 300);
    		}

    		$this->logApiCall('security_check_passed', $debugInfo, 'all_checks_passed');
    		return true;
    	}

    	/**
    	 * 获取客户端真实IP
    	 */
    	private function getClientIP(){
    		$ip = '';
    		if(!empty($_SERVER['HTTP_X_FORWARDED_FOR'])){
    			$ips = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR']);
    			$ip = trim($ips[0]);
    		} elseif(!empty($_SERVER['HTTP_X_REAL_IP'])){
    			$ip = $_SERVER['HTTP_X_REAL_IP'];
    		} elseif(!empty($_SERVER['REMOTE_ADDR'])){
    			$ip = $_SERVER['REMOTE_ADDR'];
    		}

    		// 验证IP格式
    		if(filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)){
    			return $ip;
    		}

    		return isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : '0.0.0.0';
    	}

    	/**
    	 * 记录API调用日志
    	 */
    	private function logApiCall($action, $params = array(), $result = ''){
    		$logData = array(
    			'time' => date('Y-m-d H:i:s'),
    			'ip' => $this->getClientIP(),
    			'action' => $action,
    			'params' => json_encode($params),
    			'result' => is_array($result) ? json_encode($result) : $result,
    			'user_agent' => isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : ''
    		);

    		// 写入日志文件
    		$logFile = RUNTIME_PATH . 'Logs/api_calls_' . date('Y-m-d') . '.log';
    		$logLine = implode(' | ', $logData) . "\n";
    		file_put_contents($logFile, $logLine, FILE_APPEND | LOCK_EX);
    	}

    	/**
    	 * HTTP GET请求
    	 */
    	private function httpGet($url, $timeout = 30){
    		// 使用cURL
    		if(function_exists('curl_init')){
    			$ch = curl_init();
    			curl_setopt($ch, CURLOPT_URL, $url);
    			curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    			curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
    			curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    			curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    			curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; VIP-Register-Bot/1.0)');

    			$response = curl_exec($ch);
    			$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    			curl_close($ch);

    			if($httpCode === 200 && $response !== false){
    				return $response;
    			} else {
    				throw new Exception('HTTP请求失败，状态码：' . $httpCode);
    			}
    		}
    		// 备用方案：file_get_contents
    		else if(ini_get('allow_url_fopen')){
    			$context = stream_context_create(array(
    				'http' => array(
    					'timeout' => $timeout,
    					'user_agent' => 'Mozilla/5.0 (compatible; VIP-Register-Bot/1.0)'
    				)
    			));

    			$response = file_get_contents($url, false, $context);
    			if($response !== false){
    				return $response;
    			} else {
    				throw new Exception('HTTP请求失败');
    			}
    		}
    		else {
    			throw new Exception('无可用的HTTP请求方法');
    		}
    	}

    	/**
    	 * 调试方法 - 测试API连接
    	 */
    	public function testApi(){
    		echo "<h1>API测试页面</h1>";

    		echo "<h2>1. 测试创建订单API</h2>";
    		try {
    			$url = "https://cloudshop.qnm6.top/create_order.php?customer_contact=<EMAIL>&product_id=85&pay_type=wxpay";
    			$response = $this->httpGet($url);
    			echo "<p>请求URL: " . $url . "</p>";
    			echo "<pre>" . htmlspecialchars($response) . "</pre>";
    		} catch (Exception $e) {
    			echo "<p style='color:red'>错误: " . $e->getMessage() . "</p>";
    		}

    		echo "<h2>2. 测试会员注册API</h2>";
    		try {
    			$url = "https://api.qnm6.top/admapi/vipregister.php?lx=1";
    			$response = $this->httpGet($url);
    			echo "<p>请求URL: " . $url . "</p>";
    			echo "<pre>" . htmlspecialchars($response) . "</pre>";
    		} catch (Exception $e) {
    			echo "<p style='color:red'>错误: " . $e->getMessage() . "</p>";
    		}

    		echo "<h2>3. 数据库连接测试</h2>";
    		try {
    			$orderModel = M('order_status');
    			$count = $orderModel->count();
    			echo "<p>订单状态表记录数: " . $count . "</p>";

    			// 显示表结构
    			$db = M();
    			$structure = $db->query("DESCRIBE nav_order_status");
    			echo "<h3>表结构:</h3>";
    			echo "<table border='1' style='border-collapse:collapse;'>";
    			echo "<tr><th>字段名</th><th>类型</th><th>空值</th><th>键</th><th>默认值</th></tr>";
    			foreach ($structure as $field) {
    				echo "<tr>";
    				echo "<td>" . $field['Field'] . "</td>";
    				echo "<td>" . $field['Type'] . "</td>";
    				echo "<td>" . $field['Null'] . "</td>";
    				echo "<td>" . $field['Key'] . "</td>";
    				echo "<td>" . ($field['Default'] ?: 'NULL') . "</td>";
    				echo "</tr>";
    			}
    			echo "</table>";
    		} catch (Exception $e) {
    			echo "<p style='color:red'>数据库错误: " . $e->getMessage() . "</p>";
    		}

    		echo "<h2>4. 系统信息</h2>";
    		echo "<p>PHP版本: " . PHP_VERSION . "</p>";
    		echo "<p>cURL支持: " . (function_exists('curl_init') ? '是' : '否') . "</p>";
    		echo "<p>allow_url_fopen: " . (ini_get('allow_url_fopen') ? '是' : '否') . "</p>";
    		echo "<p>当前时间: " . date('Y-m-d H:i:s') . "</p>";

    		echo "<hr><p><a href='/Index/Utils/index'>返回会员购买页面</a></p>";
    	}

    	/**
    	 * 调试接口 - 测试安全检查
    	 */
    	public function debugSecurity(){
    		echo "<h1>安全检查调试页面</h1>";

    		echo "<h2>当前请求信息</h2>";
    		echo "<table border='1' style='border-collapse:collapse;'>";
    		echo "<tr><th>项目</th><th>值</th></tr>";
    		echo "<tr><td>HTTP_HOST</td><td>" . (isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : '未设置') . "</td></tr>";
    		echo "<tr><td>HTTP_REFERER</td><td>" . (isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : '未设置') . "</td></tr>";
    		echo "<tr><td>HTTP_USER_AGENT</td><td>" . (isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '未设置') . "</td></tr>";
    		echo "<tr><td>REMOTE_ADDR</td><td>" . (isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : '未设置') . "</td></tr>";
    		echo "<tr><td>HTTP_X_FORWARDED_FOR</td><td>" . (isset($_SERVER['HTTP_X_FORWARDED_FOR']) ? $_SERVER['HTTP_X_FORWARDED_FOR'] : '未设置') . "</td></tr>";
    		echo "<tr><td>HTTP_X_REAL_IP</td><td>" . (isset($_SERVER['HTTP_X_REAL_IP']) ? $_SERVER['HTTP_X_REAL_IP'] : '未设置') . "</td></tr>";
    		echo "<tr><td>客户端IP</td><td>" . $this->getClientIP() . "</td></tr>";
    		echo "</table>";

    		echo "<h2>安全检查测试</h2>";
    		$securityResult = $this->securityCheck();
    		echo "<p>安全检查结果: " . ($securityResult ? '<span style="color:green">通过</span>' : '<span style="color:red">失败</span>') . "</p>";

    		echo "<h2>模拟创建订单测试</h2>";
    		echo "<p>测试域名访问和IP访问的差异</p>";

    		// 模拟POST请求
    		$_POST['customer_contact'] = '<EMAIL>';
    		$_POST['product_id'] = '85';
    		$_POST['pay_type'] = 'wxpay';

    		echo "<p>模拟POST参数已设置</p>";
    		echo "<p>customer_contact: " . $_POST['customer_contact'] . "</p>";
    		echo "<p>product_id: " . $_POST['product_id'] . "</p>";
    		echo "<p>pay_type: " . $_POST['pay_type'] . "</p>";

    		echo "<h2>最近的API调用日志</h2>";
    		$logFile = RUNTIME_PATH . 'Logs/api_calls_' . date('Y-m-d') . '.log';
    		if(file_exists($logFile)){
    			$lines = file($logFile);
    			$recentLines = array_slice($lines, -10); // 最近10条
    			echo "<pre>";
    			foreach($recentLines as $line){
    				echo htmlspecialchars($line);
    			}
    			echo "</pre>";
    		} else {
    			echo "<p>日志文件不存在</p>";
    		}

    		echo "<hr><p><a href='/Index/Utils/index'>返回会员购买页面</a></p>";
    	}

    	/**
    	 * 简单的测试页面
    	 */
    	public function test(){
    		$this->display();
    	}

}